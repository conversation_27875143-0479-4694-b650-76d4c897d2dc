{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_UserManagement;Username=postgres;Password=****", "RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "JwtSettings": {"Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryInMinutes": 60}, "UseRabbitMQ": true, "RabbitMQ": {"Host": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/usermanagement-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Geoapify": {"ApiKey": "********************************", "DefaultCountry": "IN"}, "Setu": {"BaseUrl": "https://dg-sandbox.setu.co", "ClientId": "292c6e76-dabf-49c4-8e48-90fba2916673", "ClientSecret": "********************************", "PanProductInstanceId": "439244ff-114e-41a8-ae74-a783f160622d", "GstProductInstanceId": "69e23f7f-4f71-412e-aec6-b1da3fb77c6f"}, "AzureComputerVision": {"Endpoint": "https://your-computer-vision-endpoint.cognitiveservices.azure.com/", "SubscriptionKey": "your-azure-computer-vision-subscription-key"}, "DocumentStorage": {"EnableEncryption": false, "FolderProtection": {"Enabled": false, "AccessPassword": "SecureFolder@2025!", "SessionTimeoutMinutes": 30, "MaxFailedAttempts": 3, "LockoutDurationMinutes": 15}, "MaxFileSizeMB": 10, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"], "RetentionPolicyDays": 2555, "BackupEnabled": true, "CompressionEnabled": false}, "AllowedHosts": "*"}