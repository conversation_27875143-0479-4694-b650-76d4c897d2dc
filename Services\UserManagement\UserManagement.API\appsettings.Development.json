{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=****", "RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "UseRabbitMQ": true, "RabbitMQ": {"Host": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest"}, "Serilog": {"MinimumLevel": {"Default": "Debug"}}, "Geoapify": {"ApiKey": "********************************", "DefaultCountry": "IN"}, "AzureComputerVision": {"Endpoint": "https://your-computer-vision-endpoint.cognitiveservices.azure.com/", "SubscriptionKey": "your-azure-computer-vision-subscription-key"}, "DocumentStorage": {"EnableEncryption": false, "FolderProtection": {"Enabled": true, "AccessPassword": "DevFolder@2025!", "SessionTimeoutMinutes": 60, "MaxFailedAttempts": 5, "LockoutDurationMinutes": 10}, "MaxFileSizeMB": 10, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"], "RetentionPolicyDays": 365, "BackupEnabled": false, "CompressionEnabled": false}}