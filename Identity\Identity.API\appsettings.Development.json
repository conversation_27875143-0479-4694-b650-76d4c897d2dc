{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=****"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Debug", "Microsoft.Hosting.Lifetime": "Information"}}, "UseRabbitMQ": true, "RabbitMQ": {"Host": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest"}, "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"], "Services": {"SubscriptionManagement": {"BaseUrl": "http://**************:5003"}}}